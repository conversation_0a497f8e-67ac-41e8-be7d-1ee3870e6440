import os
import os.path as path

import deeplabcut as dlc

config_path = r'C:\\Users\\<USER>\\PycharmProjects\\PupilExtraction\\barthlab_pupil_by_max-pupil_extraction-2025-07-04\\config.yaml'

def simple_labeling_workflow():
    """
    Simplified approach: Process one video at a time without mixing GUI and console input.
    This completely avoids the deadlock issue.
    """
    labeled_data_path = os.path.join(
        r'C:\\Users\\<USER>\\PycharmProjects\\PupilExtraction\\barthlab_pupil_by_max-pupil_extraction-2025-07-04',
        "labeled-data"
    )
    
    print("DeepLabCut Simple Labeling Script")
    print("=" * 50)
    print("This script will open the labeling GUI for each video folder.")
    print("Complete your labeling and close the GUI to continue.")
    print("=" * 50)
    
    # Get all video folders
    video_folders = []
    for dir_path, dirnames, filenames in os.walk(labeled_data_path):
        for video_name in dirnames:
            video_folder_path = path.join(dir_path, video_name)
            video_folders.append((video_folder_path, video_name))
        break  # Only process the top level
    
    if not video_folders:
        print("No video folders found. Make sure you have extracted frames first.")
        return
    
    print(f"Found {len(video_folders)} video folders:")
    for i, (_, name) in enumerate(video_folders, 1):
        print(f"  {i}. {name}")
    
    print(f"\nStarting labeling process...")
    print("The GUI will open for each video. Close it when done to proceed to the next.")
    input("Press Enter to start...")
    
    # Process each video
    for i, (video_folder_path, video_name) in enumerate(video_folders, 1):
        print(f"\n{'='*60}")
        print(f"Processing video {i}/{len(video_folders)}: {video_name}")
        print(f"{'='*60}")
        print("Opening labeling GUI...")
        print("CLOSE the GUI window when you finish labeling to continue.")
        
        try:
            # This should open the GUI and block until closed
            dlc.label_frames(config_path, video_folder_path)
            print(f"✓ Completed labeling for: {video_name}")
        except Exception as e:
            print(f"❌ Error with {video_name}: {e}")
            continue
    
    print("\n" + "=" * 60)
    print("All videos processed!")
    print("Next steps:")
    print("1. Check labels: dlc.check_labels(config_path)")
    print("2. Create training dataset: dlc.create_training_dataset(config_path)")

if __name__ == "__main__":
    simple_labeling_workflow()
