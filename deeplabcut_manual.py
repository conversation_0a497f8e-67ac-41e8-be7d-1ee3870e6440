import os
import os.path as path

config_path = r'C:\\Users\\<USER>\\PycharmProjects\\PupilExtraction\\barthlab_pupil_by_max-pupil_extraction-2025-07-04\\config.yaml'

def create_individual_labeling_scripts():
    """
    Create individual Python scripts for each video folder.
    This completely avoids any deadlock issues by running each labeling session separately.
    """
    labeled_data_path = os.path.join(
        r'C:\\Users\\<USER>\\PycharmProjects\\PupilExtraction\\barthlab_pupil_by_max-pupil_extraction-2025-07-04',
        "labeled-data"
    )
    
    print("DeepLabCut Manual Labeling Script Generator")
    print("=" * 60)
    
    # Get all video folders
    video_folders = []
    for dir_path, dirnames, filenames in os.walk(labeled_data_path):
        for video_name in dirnames:
            video_folder_path = path.join(dir_path, video_name)
            video_folders.append((video_folder_path, video_name))
        break  # Only process the top level
    
    if not video_folders:
        print("No video folders found. Make sure you have extracted frames first.")
        return
    
    print(f"Found {len(video_folders)} video folders:")
    for i, (_, name) in enumerate(video_folders, 1):
        print(f"  {i}. {name}")
    
    # Create individual scripts
    script_dir = "labeling_scripts"
    os.makedirs(script_dir, exist_ok=True)
    
    print(f"\nCreating individual labeling scripts in '{script_dir}' folder...")
    
    for i, (video_folder_path, video_name) in enumerate(video_folders, 1):
        script_name = f"label_{video_name}.py"
        script_path = os.path.join(script_dir, script_name)
        
        script_content = f'''#!/usr/bin/env python3
"""
DeepLabCut Labeling Script for: {video_name}
Generated automatically - run this script to label frames for this video.
"""

import deeplabcut as dlc

# Configuration
config_path = r"{config_path}"
video_folder_path = r"{video_folder_path}"
video_name = "{video_name}"

print("=" * 60)
print(f"DeepLabCut Labeling for: {{video_name}}")
print("=" * 60)
print("Instructions:")
print("1. The labeling GUI will open")
print("2. Complete your labeling work")
print("3. Save your work (Ctrl+S or File > Save)")
print("4. Close the GUI window when finished")
print("5. The script will then complete")
print("=" * 60)

input("Press Enter to open the labeling GUI...")

try:
    print("Opening DeepLabCut labeling GUI...")
    dlc.label_frames(config_path, video_folder_path)
    print("SUCCESS: Labeling completed successfully!")
except Exception as e:
    print(f"ERROR: Error during labeling: {{e}}")
    input("Press Enter to exit...")

print("\\nLabeling session finished for: {{video_name}}")
input("Press Enter to exit...")
'''
        
        with open(script_path, 'w', encoding='utf-8') as f:
            f.write(script_content)
        
        print(f"  Created: {script_path}")
    
    # Create a master script to run all labeling scripts in sequence
    master_script_path = os.path.join(script_dir, "run_all_labeling.py")
    master_content = f'''#!/usr/bin/env python3
"""
Master script to run all labeling scripts in sequence.
This ensures no deadlock issues by running each video separately.
"""

import subprocess
import sys
import os

scripts = [
'''
    
    for i, (_, video_name) in enumerate(video_folders):
        master_content += f'    "label_{video_name}.py",\n'
    
    master_content += f''']

print("DeepLabCut Sequential Labeling")
print("=" * 50)
print(f"Will process {{len(scripts)}} videos in sequence")
print("Each video will open in a separate labeling session")
print("=" * 50)

for i, script in enumerate(scripts, 1):
    print(f"\\n{{'-'*50}}")
    print(f"Starting labeling session {{i}}/{{len(scripts)}}: {{script}}")
    print(f"{{'-'*50}}")
    
    try:
        result = subprocess.run([sys.executable, script], check=True)
        print(f"SUCCESS: Completed: {{script}}")
    except subprocess.CalledProcessError as e:
        print(f"ERROR: Error with {{script}}: {{e}}")
        choice = input("Continue with next video? [y]/n: ").strip().lower()
        if choice == 'n':
            break

print("\\n" + "=" * 50)
print("All labeling sessions completed!")
print("Next steps:")
print("1. Check labels: dlc.check_labels(config_path)")
print("2. Create training dataset: dlc.create_training_dataset(config_path)")
'''
    
    with open(master_script_path, 'w', encoding='utf-8') as f:
        f.write(master_content)
    
    print(f"\nCreated master script: {master_script_path}")
    
    print("\n" + "=" * 60)
    print("USAGE OPTIONS:")
    print("=" * 60)
    print("Option 1 - Run individual scripts:")
    for i, (_, video_name) in enumerate(video_folders, 1):
        print(f"  python {script_dir}/label_{video_name}.py")
    
    print(f"\nOption 2 - Run all scripts in sequence:")
    print(f"  python {master_script_path}")
    
    print("\n" + "=" * 60)
    print("BENEFITS of this approach:")
    print("- No deadlock issues")
    print("- Each video runs in isolation")
    print("- Can resume from any point")
    print("- Clear progress tracking")
    print("=" * 60)

if __name__ == "__main__":
    create_individual_labeling_scripts()
