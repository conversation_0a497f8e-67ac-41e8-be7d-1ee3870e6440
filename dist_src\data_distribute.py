import time
import os.path as path
from glob import glob
from setting import *
import numpy as np
import pandas as pd

import matplotlib.pyplot as plt

for dir_path, _ , _ in os.walk(path.join(TargetFilePath, TaskFolder)):
    for file_path in glob(path.join(dir_path, "*_proc.npy")):
        tmp_dir, tmp_file = path.dirname(file_path), path.basename(file_path)
        target_path = path.join(tmp_dir, "..", "pupil", tmp_file.replace("VIDEO", "PUPIL")
                                .replace("_proc.npy", ".csv"))
        print(target_path)
        tmp_data = np.load(file_path, allow_pickle=True).item()['pupil'][0]

        save_dict = pd.DataFrame({"Radius": 2*np.sqrt(tmp_data['area_smooth']/np.pi)})
        save_dict.to_csv(target_path)

        if DEBUG:
            area_trace = 2*np.sqrt(tmp_data['area_smooth']/np.pi)
            com_trace = tmp_data['com_smooth']
            plt.plot(area_trace, label="area")
            plt.plot(com_trace[:, 0], label="x")
            plt.plot(com_trace[:, 1], label="y")
            plt.legend()
            plt.show()
            plt.close()
