#!/usr/bin/env python3
"""
DeepLabCut Labeling Script for: mouse_video_ROH12F_D13_S50_P2
Generated automatically - run this script to label frames for this video.
"""

import deeplabcut as dlc

# Configuration
config_path = r"C:\\Users\\<USER>\\PycharmProjects\\PupilExtraction\\barthlab_pupil_by_max-pupil_extraction-2025-07-04\\config.yaml"
video_folder_path = r"C:\\Users\\<USER>\\PycharmProjects\\PupilExtraction\\barthlab_pupil_by_max-pupil_extraction-2025-07-04\labeled-data\mouse_video_ROH12F_D13_S50_P2"
video_name = "mouse_video_ROH12F_D13_S50_P2"

print("=" * 60)
print(f"DeepLabCut Labeling for: {video_name}")
print("=" * 60)
print("Instructions:")
print("1. The labeling GUI will open")
print("2. Complete your labeling work")
print("3. Save your work (Ctrl+S or File > Save)")
print("4. Close the GUI window when finished")
print("5. The script will then complete")
print("=" * 60)

input("Press Enter to open the labeling GUI...")

try:
    print("Opening DeepLabCut labeling GUI...")
    dlc.label_frames(config_path, video_folder_path)
    print("SUCCESS: Labeling completed successfully!")
except Exception as e:
    print(f"ERROR: Error during labeling: {e}")
    input("Press Enter to exit...")

print("\nLabeling session finished for: {video_name}")
input("Press Enter to exit...")
