#!/usr/bin/env python3
"""
Test script to verify the DeepLabCut labeling solution works without deadlock.
This will test one video to make sure the GUI opens properly.
"""

import subprocess
import sys
import os

def test_single_video_labeling():
    """
    Test labeling for a single video to verify the solution works.
    """
    print("DeepLabCut Labeling Solution Test")
    print("=" * 50)
    
    # Check if labeling scripts exist
    scripts_dir = "labeling_scripts"
    if not os.path.exists(scripts_dir):
        print("ERROR: labeling_scripts directory not found!")
        print("Please run 'python deeplabcut_manual.py' first to generate the scripts.")
        return False
    
    # Get the first script to test
    script_files = [f for f in os.listdir(scripts_dir) if f.startswith("label_") and f.endswith(".py")]
    if not script_files:
        print("ERROR: No labeling scripts found!")
        return False
    
    test_script = script_files[0]  # Test with the first video
    script_path = os.path.join(scripts_dir, test_script)
    
    print(f"Testing with script: {test_script}")
    print("This will open the DeepLabCut GUI for one video.")
    print("\nInstructions:")
    print("1. The GUI should open without any console prompts")
    print("2. You can interact with the GUI normally")
    print("3. Close the GUI when you're done testing")
    print("4. The script should complete without hanging")
    
    choice = input("\nProceed with test? [y]/n: ").strip().lower()
    if choice == 'n':
        print("Test cancelled.")
        return False
    
    print(f"\nRunning test script: {script_path}")
    print("=" * 50)
    
    try:
        # Run the test script
        result = subprocess.run([sys.executable, script_path], check=True)
        print("=" * 50)
        print("SUCCESS: Test completed without deadlock!")
        print("The solution is working correctly.")
        return True
    except subprocess.CalledProcessError as e:
        print("=" * 50)
        print(f"ERROR: Test failed with error: {e}")
        return False
    except KeyboardInterrupt:
        print("\nTest interrupted by user.")
        return False

if __name__ == "__main__":
    success = test_single_video_labeling()
    if success:
        print("\n" + "=" * 50)
        print("SOLUTION VERIFIED!")
        print("You can now use the labeling scripts:")
        print("- Individual videos: python labeling_scripts/label_[video_name].py")
        print("- All videos: python labeling_scripts/run_all_labeling.py")
    else:
        print("\nTest failed. Please check the error messages above.")
