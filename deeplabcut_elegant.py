#!/usr/bin/env python3
"""
DeepLabCut Elegant Labeling Solution
====================================

A clean, maintainable solution for labeling DeepLabCut videos without GUI/console deadlock.
The key insight: Separate the user selection phase from the GUI execution phase completely.

Key Features:
- Single script that handles all videos
- Automatic video detection
- No GUI/console deadlock issues
- User-friendly interface
- Easy to maintain and extend
"""

import os
from typing import List, Tuple

import deeplabcut as dlc

# Configuration
config_path = r'C:\\Users\\<USER>\\PycharmProjects\\PupilExtraction\\barthlab_pupil_by_max-pupil_extraction-2025-07-04\\config.yaml'

class DeepLabCutLabeler:
    """
    Elegant DeepLabCut labeling manager that prevents GUI/console deadlock.
    """
    
    def __init__(self, config_path: str):
        self.config_path = config_path
        self.labeled_data_path = self._get_labeled_data_path()
        
    def _get_labeled_data_path(self) -> str:
        """Get the labeled-data directory path from config."""
        config_dir = os.path.dirname(self.config_path)
        return os.path.join(config_dir, "labeled-data")
    
    def discover_videos(self) -> List[Tuple[str, str]]:
        """
        Automatically discover all video folders in the labeled-data directory.
        Returns list of (video_folder_path, video_name) tuples.
        """
        video_folders = []
        
        if not os.path.exists(self.labeled_data_path):
            print(f"Warning: labeled-data directory not found: {self.labeled_data_path}")
            return video_folders
        
        for item in os.listdir(self.labeled_data_path):
            item_path = os.path.join(self.labeled_data_path, item)
            if os.path.isdir(item_path):
                video_folders.append((item_path, item))
        
        # Sort for consistent ordering
        video_folders.sort(key=lambda x: x[1])
        return video_folders
    
    def display_video_menu(self, videos: List[Tuple[str, str]]) -> None:
        """Display a user-friendly menu of available videos."""
        print(f"\n{'='*60}")
        print("DEEPLABCUT VIDEO LABELING MENU")
        print(f"{'='*60}")
        print(f"Found {len(videos)} video folders:")
        print()
        
        for i, (_, video_name) in enumerate(videos, 1):
            print(f"  {i:2d}. {video_name}")
        
        print(f"\n{'='*60}")
    
    def get_user_selection(self, videos: List[Tuple[str, str]]) -> List[int]:
        """
        Get user selection for which videos to label.
        Returns list of video indices (0-based).
        """
        while True:
            print("\nSelection options:")
            print("  - Enter video numbers (e.g., '1,3,5' or '1-5')")
            print("  - Enter 'all' to label all videos")
            print("  - Enter 'q' to quit")
            
            try:
                selection = input("\nYour selection: ").strip().lower()
                
                if selection == 'q':
                    return []
                elif selection == 'all':
                    return list(range(len(videos)))
                else:
                    # Parse number ranges and individual numbers
                    indices = []
                    parts = selection.split(',')
                    
                    for part in parts:
                        part = part.strip()
                        if '-' in part:
                            # Handle ranges like "1-5"
                            start, end = part.split('-', 1)
                            start_idx = int(start.strip()) - 1  # Convert to 0-based
                            end_idx = int(end.strip()) - 1      # Convert to 0-based
                            indices.extend(range(start_idx, end_idx + 1))
                        else:
                            # Handle individual numbers
                            indices.append(int(part) - 1)  # Convert to 0-based
                    
                    # Validate indices
                    valid_indices = []
                    for idx in indices:
                        if 0 <= idx < len(videos):
                            valid_indices.append(idx)
                        else:
                            print(f"Warning: Invalid video number {idx + 1}, skipping...")
                    
                    if valid_indices:
                        return sorted(set(valid_indices))  # Remove duplicates and sort
                    else:
                        print("No valid video numbers selected.")
                        
            except (ValueError, IndexError) as e:
                print(f"Invalid input: {e}. Please try again.")
            except KeyboardInterrupt:
                print("\nOperation cancelled.")
                return []

    def label_videos_sequentially(self, videos: List[Tuple[str, str]], selected_indices: List[int]) -> None:
        """
        Label videos sequentially without any console input during GUI operation.
        This is the key to avoiding deadlock: NO console input while GUI is active.
        """
        print(f"\n{'='*60}")
        print("STARTING SEQUENTIAL LABELING")
        print(f"{'='*60}")
        print(f"Will process {len(selected_indices)} videos in sequence.")
        print()
        print("IMPORTANT INSTRUCTIONS:")
        print("- Each video's GUI will open automatically")
        print("- Complete your labeling work in the GUI")
        print("- Save your work (Ctrl+S or File > Save)")
        print("- CLOSE the GUI window to proceed to the next video")
        print("- NO console prompts will appear while GUI is open")
        print(f"{'='*60}")
        
        input("\nPress Enter to start the sequential labeling process...")
        
        successful = 0
        failed = 0
        
        for i, video_idx in enumerate(selected_indices, 1):
            video_folder_path, video_name = videos[video_idx]
            
            print(f"\n{'-'*60}")
            print(f"Video {i}/{len(selected_indices)}: {video_name}")
            print(f"{'-'*60}")
            print("Opening labeling GUI...")
            print("Complete your labeling and CLOSE the GUI to continue.")
            print(f"{'-'*60}")
            
            try:
                # This is the simple, direct approach - let DLC handle the blocking
                dlc.label_frames(self.config_path, video_folder_path)
                print(f"✓ Completed labeling for: {video_name}")
                successful += 1
                
            except Exception as e:
                print(f"✗ Error during labeling for {video_name}: {e}")
                failed += 1
        
        # Summary
        print(f"\n{'='*60}")
        print("LABELING SESSION SUMMARY")
        print(f"{'='*60}")
        print(f"Successfully labeled: {successful} videos")
        print(f"Failed: {failed} videos")
        print(f"Total processed: {successful + failed} videos")
        
        if successful > 0:
            print(f"\n{'='*60}")
            print("NEXT STEPS:")
            print(f"{'='*60}")
            print("1. Check your labeled data:")
            print(f"   dlc.check_labels(r'{self.config_path}')")
            print("2. Create training dataset:")
            print(f"   dlc.create_training_dataset(r'{self.config_path}')")
            print("3. Train the network:")
            print(f"   dlc.train_network(r'{self.config_path}')")

    def run_labeling_session(self) -> None:
        """
        Main method to run the labeling session with user interaction.
        """
        print("DeepLabCut Elegant Labeling Solution")
        print("=" * 60)
        print("Automatically detecting videos...")
        
        # Discover all videos
        videos = self.discover_videos()
        
        if not videos:
            print("No video folders found in the labeled-data directory.")
            print("Make sure you have extracted frames first using:")
            print("dlc.extract_frames(config_path, 'automatic', 'kmeans', crop=True)")
            return
        
        # Display menu and get user selection
        self.display_video_menu(videos)
        selected_indices = self.get_user_selection(videos)
        
        if not selected_indices:
            print("No videos selected. Exiting.")
            return
        
        # Show selected videos for confirmation
        print(f"\nSelected videos for labeling:")
        for i, video_idx in enumerate(selected_indices, 1):
            _, video_name = videos[video_idx]
            print(f"  {i}. {video_name}")
        
        try:
            confirm = input(f"\nProceed with labeling {len(selected_indices)} videos? [y]/n: ").strip().lower()
            if confirm == 'n':
                print("Labeling cancelled.")
                return
        except KeyboardInterrupt:
            print("\nOperation cancelled.")
            return
        
        # Process selected videos sequentially
        self.label_videos_sequentially(videos, selected_indices)


def main():
    """Main function to run the elegant labeling solution."""
    try:
        labeler = DeepLabCutLabeler(config_path)
        labeler.run_labeling_session()
    except KeyboardInterrupt:
        print("\nProgram interrupted by user.")
    except Exception as e:
        print(f"Error: {e}")
        print("Please check your configuration and try again.")


if __name__ == "__main__":
    main()
