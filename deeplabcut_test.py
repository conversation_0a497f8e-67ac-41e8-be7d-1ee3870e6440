import os
import os.path as path
import time

import deeplabcut as dlc

config_path = r'C:\\Users\\<USER>\\PycharmProjects\\PupilExtraction\\barthlab_pupil_by_max-pupil_extraction-2025-07-04\\config.yaml'

# dlc.extract_frames(config_path, 'automatic','kmeans', crop=True, userfeedback=True)
# dlc.generate_training_dataset.frame_extraction.select_cropping_area(config_path)

for dir_path, dirnames, filenames in os.walk(os.path.join(
  r'C:\\Users\\<USER>\\PycharmProjects\\PupilExtraction\\barthlab_pupil_by_max-pupil_extraction-2025-07-04',
  "labeled-data")):
    print(dir_path, dirnames, filenames)
    for video_name in dirnames:
        print(video_name)
        dlc.label_frames(config_path, path.join(dir_path, video_name))
        while True:
            if input("continue? [y]/n") == "y":
                break
            else:
                time.sleep(10)
    break
