import os
import os.path as path
import time
import psutil
import subprocess
import sys

import deeplabcut as dlc

config_path = r'C:\\Users\\<USER>\\PycharmProjects\\PupilExtraction\\barthlab_pupil_by_max-pupil_extraction-2025-07-04\\config.yaml'

# dlc.extract_frames(config_path, 'automatic','kmeans', crop=True, userfeedback=True)
# dlc.generate_training_dataset.frame_extraction.select_cropping_area(config_path)

def wait_for_napari_to_close():
    """
    Wait for napari processes to close by monitoring running processes.
    This ensures we don't proceed until the GUI is actually closed.
    """
    print("Monitoring napari processes...")

    while True:
        napari_running = False
        try:
            # Check for napari processes
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    proc_info = proc.info
                    if proc_info['name'] and 'napari' in proc_info['name'].lower():
                        napari_running = True
                        break
                    if proc_info['cmdline']:
                        cmdline_str = ' '.join(proc_info['cmdline']).lower()
                        if 'napari' in cmdline_str or 'deeplabcut' in cmdline_str:
                            napari_running = True
                            break
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    continue
        except Exception as e:
            print(f"Error monitoring processes: {e}")
            break

        if not napari_running:
            print("✓ Napari GUI has been closed")
            break

        time.sleep(1)  # Check every second

def label_video_frames_interactive():
    """
    Interactive labeling session that handles one video at a time.
    This approach avoids the deadlock by not mixing GUI and console input.
    """
    labeled_data_path = os.path.join(
        r'C:\\Users\\<USER>\\PycharmProjects\\PupilExtraction\\barthlab_pupil_by_max-pupil_extraction-2025-07-04',
        "labeled-data"
    )

    print("DeepLabCut Frame Labeling Script")
    print("=" * 40)
    print(f"Looking for video folders in: {labeled_data_path}")

    video_folders = []
    for dir_path, dirnames, filenames in os.walk(labeled_data_path):
        print(f"Found directory: {dir_path}")
        print(f"Subdirectories: {dirnames}")

        for video_name in dirnames:
            video_folder_path = path.join(dir_path, video_name)
            video_folders.append((video_folder_path, video_name))
        break  # Only process the top level

    if not video_folders:
        print("No video folders found. Make sure you have extracted frames first.")
        return

    print(f"\nFound {len(video_folders)} video folders to process:")
    for i, (folder_path, name) in enumerate(video_folders, 1):
        print(f"  {i}. {name}")

    # Process videos one by one with user confirmation
    for i, (video_folder_path, video_name) in enumerate(video_folders, 1):
        print(f"\n{'='*60}")
        print(f"Video {i}/{len(video_folders)}: {video_name}")
        print(f"{'='*60}")

        # Ask user if they want to process this video
        while True:
            try:
                choice = input(f"Process '{video_name}'? [y]es/[n]o/[q]uit: ").strip().lower()
                if choice in ['y', 'yes', '']:
                    break
                elif choice in ['n', 'no']:
                    print(f"Skipping {video_name}")
                    continue
                elif choice in ['q', 'quit']:
                    print("Quitting labeling process.")
                    return
                else:
                    print("Please enter 'y', 'n', or 'q'")
            except KeyboardInterrupt:
                print("\nProcess interrupted by user.")
                return

        if choice in ['n', 'no']:
            continue

        # Now open the GUI without any further prompts
        print(f"\nOpening labeling GUI for: {video_name}")
        print("INSTRUCTIONS:")
        print("1. Complete your labeling work in the GUI")
        print("2. Save your work (Ctrl+S or File > Save)")
        print("3. CLOSE the GUI window when finished")
        print("4. The script will automatically detect when you close the GUI")
        print("\nStarting GUI...")

        try:
            # Launch the GUI in a separate process to avoid blocking issues
            python_executable = sys.executable
            script_content = f'''
import deeplabcut as dlc
config_path = r"{config_path}"
video_folder_path = r"{video_folder_path}"
print("Opening DeepLabCut labeling GUI...")
dlc.label_frames(config_path, video_folder_path)
print("GUI closed.")
'''

            # Write temporary script
            temp_script = "temp_label_frames.py"
            with open(temp_script, 'w') as f:
                f.write(script_content)

            # Run the labeling GUI in a separate process
            print("Launching DeepLabCut GUI...")
            process = subprocess.Popen([python_executable, temp_script],
                                     stdout=subprocess.PIPE,
                                     stderr=subprocess.PIPE,
                                     text=True)

            # Wait for the process to complete
            print("Waiting for you to complete labeling and close the GUI...")
            stdout, stderr = process.communicate()

            # Clean up temp script
            try:
                os.remove(temp_script)
            except:
                pass

            if process.returncode == 0:
                print(f"✓ Labeling completed for {video_name}")
            else:
                print(f"❌ Error during labeling for {video_name}")
                if stderr:
                    print(f"Error details: {stderr}")

        except Exception as e:
            print(f"❌ Error opening labeling GUI for {video_name}: {e}")
            continue

    print("\n" + "=" * 60)
    print("Frame labeling process completed!")
    print("Next steps:")
    print("1. Check your labeled data with: dlc.check_labels(config_path)")
    print("2. Create training dataset with: dlc.create_training_dataset(config_path)")
    print("3. Train the network with: dlc.train_network(config_path)")

# Main execution
if __name__ == "__main__":
    label_video_frames_interactive()
