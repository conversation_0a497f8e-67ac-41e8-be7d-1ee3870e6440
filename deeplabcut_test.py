import os
import os.path as path

import deeplabcut as dlc

config_path = r'C:\\Users\\<USER>\\PycharmProjects\\PupilExtraction\\barthlab_pupil_by_max-pupil_extraction-2025-07-04\\config.yaml'

# dlc.extract_frames(config_path, 'automatic','kmeans', crop=True, userfeedback=True)
# dlc.generate_training_dataset.frame_extraction.select_cropping_area(config_path)

for dir_path, dirnames, filenames in os.walk(os.path.join(
  r'C:\\Users\\<USER>\\PycharmProjects\\PupilExtraction\\barthlab_pupil_by_max-pupil_extraction-2025-07-04',
  "labeled-data")):
    print(dir_path, dirnames, filenames)
    for video_name in dirnames:
        print(f"Opening labeling GUI for video: {video_name}")
        print("Please complete labeling in the GUI and close it when done.")
        print("The script will automatically continue to the next video.")

        # This will open the GUI and wait for the user to complete labeling and close it
        dlc.label_frames(config_path, path.join(dir_path, video_name))

        print(f"Labeling completed for {video_name}")

        # Optional: Ask if user wants to continue to next video
        try:
            user_input = input("Continue to next video? [y]/n: ").strip().lower()
            if user_input == 'n':
                print("Stopping labeling process.")
                break
        except KeyboardInterrupt:
            print("\nLabeling process interrupted by user.")
            break
    break
