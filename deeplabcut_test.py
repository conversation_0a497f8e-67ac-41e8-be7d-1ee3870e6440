import os
import os.path as path

import deeplabcut as dlc

config_path = r'C:\\Users\\<USER>\\PycharmProjects\\PupilExtraction\\barthlab_pupil_by_max-pupil_extraction-2025-07-04\\config.yaml'

# dlc.extract_frames(config_path, 'automatic','kmeans', crop=True, userfeedback=True)
# dlc.generate_training_dataset.frame_extraction.select_cropping_area(config_path)

def label_video_frames(config_path, video_folder_path, video_name):
    """
    Label frames for a single video using DeepLabCut GUI.
    This function handles the labeling process properly.
    """
    print(f"\n=== Processing video: {video_name} ===")
    print("About to open the DeepLabCut labeling GUI.")
    print("IMPORTANT Instructions:")
    print("1. Complete your labeling work in the GUI")
    print("2. Save your work (Ctrl+S or File > Save)")
    print("3. CLOSE the GUI window when finished")
    print("4. The script will automatically continue to the next video")
    print("-" * 50)

    # Ask user if they want to proceed with this video
    try:
        proceed = input(f"Open labeling GUI for '{video_name}'? [y]/n: ").strip().lower()
        if proceed == 'n':
            print(f"Skipping {video_name}")
            return False
    except KeyboardInterrupt:
        print("\nProcess interrupted by user.")
        return False

    print(f"Opening labeling GUI for {video_name}...")
    print("Waiting for you to complete labeling and close the GUI...")

    # Open the labeling GUI - this should block until GUI is closed
    try:
        # Use the correct DeepLabCut function call
        dlc.label_frames(config_path, video_folder_path)
        print(f"✓ Labeling GUI closed for {video_name}")
        print(f"✓ Labeling session completed for {video_name}")
        return True
    except Exception as e:
        print(f"❌ Error opening labeling GUI for {video_name}: {e}")
        return False

# Main processing loop
labeled_data_path = os.path.join(
    r'C:\\Users\\<USER>\\PycharmProjects\\PupilExtraction\\barthlab_pupil_by_max-pupil_extraction-2025-07-04',
    "labeled-data"
)

print("DeepLabCut Frame Labeling Script")
print("=" * 40)
print(f"Looking for video folders in: {labeled_data_path}")

video_folders = []
for dir_path, dirnames, filenames in os.walk(labeled_data_path):
    print(f"Found directory: {dir_path}")
    print(f"Subdirectories: {dirnames}")
    print(f"Files: {filenames}")

    for video_name in dirnames:
        video_folder_path = path.join(dir_path, video_name)
        video_folders.append((video_folder_path, video_name))
    break  # Only process the top level

print(f"\nFound {len(video_folders)} video folders to process:")
for i, (folder_path, name) in enumerate(video_folders, 1):
    print(f"  {i}. {name}")

if not video_folders:
    print("No video folders found. Make sure you have extracted frames first.")
    exit(1)

# Process each video folder
for video_folder_path, video_name in video_folders:
    success = label_video_frames(config_path, video_folder_path, video_name)
    if not success:
        print("Stopping due to error or user request.")
        break

print("\n" + "=" * 40)
print("Frame labeling process completed!")
print("Next steps:")
print("1. Check your labeled data with: dlc.check_labels(config_path)")
print("2. Create training dataset with: dlc.create_training_dataset(config_path)")
print("3. Train the network with: dlc.train_network(config_path)")
