#!/usr/bin/env python3
"""
DeepLabCut Elegant Labeling Solution
====================================

A clean, maintainable solution for labeling DeepLabCut videos without GUI/console deadlock.
Automatically detects videos and provides an intuitive interface for labeling.

Key Features:
- Single script that handles all videos
- Automatic video detection
- No GUI/console deadlock issues
- User-friendly interface
- Easy to maintain and extend
"""

import os
import subprocess
import sys
import tempfile
from typing import List, Tuple

# Configuration
config_path = r'C:\\Users\\<USER>\\PycharmProjects\\PupilExtraction\\barthlab_pupil_by_max-pupil_extraction-2025-07-04\\config.yaml'

class DeepLabCutLabeler:
    """
    Elegant DeepLabCut labeling manager that prevents GUI/console deadlock.
    """

    def __init__(self, config_path: str):
        self.config_path = config_path
        self.labeled_data_path = self._get_labeled_data_path()

    def _get_labeled_data_path(self) -> str:
        """Get the labeled-data directory path from config."""
        config_dir = os.path.dirname(self.config_path)
        return os.path.join(config_dir, "labeled-data")

    def discover_videos(self) -> List[Tuple[str, str]]:
        """
        Automatically discover all video folders in the labeled-data directory.
        Returns list of (video_folder_path, video_name) tuples.
        """
        video_folders = []

        if not os.path.exists(self.labeled_data_path):
            print(f"Warning: labeled-data directory not found: {self.labeled_data_path}")
            return video_folders

        for item in os.listdir(self.labeled_data_path):
            item_path = os.path.join(self.labeled_data_path, item)
            if os.path.isdir(item_path):
                video_folders.append((item_path, item))

        # Sort for consistent ordering
        video_folders.sort(key=lambda x: x[1])
        return video_folders

    def label_single_video_isolated(self, video_folder_path: str, video_name: str) -> bool:
        """
        Label a single video using subprocess isolation to prevent deadlock.
        This completely separates the GUI from console input.
        """
        print(f"\n{'='*60}")
        print(f"Opening labeling GUI for: {video_name}")
        print(f"{'='*60}")
        print("Instructions:")
        print("1. The DeepLabCut GUI will open in a separate process")
        print("2. Complete your labeling work")
        print("3. Save your work (Ctrl+S or File > Save)")
        print("4. Close the GUI window when finished")
        print("5. The script will automatically continue")
        print(f"{'='*60}")

        # Create a temporary script for this specific video
        temp_script_content = f'''
import deeplabcut as dlc
import sys

config_path = r"{self.config_path}"
video_folder_path = r"{video_folder_path}"

print("Opening DeepLabCut labeling GUI...")
try:
    dlc.label_frames(config_path, video_folder_path)
    print("Labeling GUI closed successfully.")
    sys.exit(0)
except Exception as e:
    print(f"Error: {{e}}")
    sys.exit(1)
'''

        # Write and execute the temporary script
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False, encoding='utf-8') as temp_file:
            temp_file.write(temp_script_content)
            temp_script_path = temp_file.name

        try:
            print("Starting labeling session...")
            result = subprocess.run(
                [sys.executable, temp_script_path],
                capture_output=False,  # Allow GUI to display properly
                text=True
            )

            if result.returncode == 0:
                print(f"✓ Successfully completed labeling for: {video_name}")
                return True
            else:
                print(f"✗ Error during labeling for: {video_name}")
                return False

        except Exception as e:
            print(f"✗ Failed to start labeling for {video_name}: {e}")
            return False
        finally:
            # Clean up temporary script
            try:
                os.unlink(temp_script_path)
            except:
                pass

    def display_video_menu(self, videos: List[Tuple[str, str]]) -> None:
        """Display a user-friendly menu of available videos."""
        print(f"\n{'='*60}")
        print("DEEPLABCUT VIDEO LABELING MENU")
        print(f"{'='*60}")
        print(f"Found {len(videos)} video folders:")
        print()

        for i, (_, video_name) in enumerate(videos, 1):
            print(f"  {i:2d}. {video_name}")

        print(f"\n{'='*60}")

    def get_user_selection(self, videos: List[Tuple[str, str]]) -> List[int]:
        """
        Get user selection for which videos to label.
        Returns list of video indices (0-based).
        """
        while True:
            print("\nSelection options:")
            print("  - Enter video numbers (e.g., '1,3,5' or '1-5')")
            print("  - Enter 'all' to label all videos")
            print("  - Enter 'q' to quit")

            try:
                selection = input("\nYour selection: ").strip().lower()

                if selection == 'q':
                    return []
                elif selection == 'all':
                    return list(range(len(videos)))
                else:
                    # Parse number ranges and individual numbers
                    indices = []
                    parts = selection.split(',')

                    for part in parts:
                        part = part.strip()
                        if '-' in part:
                            # Handle ranges like "1-5"
                            start, end = part.split('-', 1)
                            start_idx = int(start.strip()) - 1  # Convert to 0-based
                            end_idx = int(end.strip()) - 1      # Convert to 0-based
                            indices.extend(range(start_idx, end_idx + 1))
                        else:
                            # Handle individual numbers
                            indices.append(int(part) - 1)  # Convert to 0-based

                    # Validate indices
                    valid_indices = []
                    for idx in indices:
                        if 0 <= idx < len(videos):
                            valid_indices.append(idx)
                        else:
                            print(f"Warning: Invalid video number {idx + 1}, skipping...")

                    if valid_indices:
                        return sorted(set(valid_indices))  # Remove duplicates and sort
                    else:
                        print("No valid video numbers selected.")

            except (ValueError, IndexError) as e:
                print(f"Invalid input: {e}. Please try again.")
            except KeyboardInterrupt:
                print("\nOperation cancelled.")
                return []

    def run_labeling_session(self) -> None:
        """
        Main method to run the labeling session with user interaction.
        """
        print("DeepLabCut Elegant Labeling Solution")
        print("=" * 60)
        print("Automatically detecting videos...")

        # Discover all videos
        videos = self.discover_videos()

        if not videos:
            print("No video folders found in the labeled-data directory.")
            print("Make sure you have extracted frames first using:")
            print("dlc.extract_frames(config_path, 'automatic', 'kmeans', crop=True)")
            return

        # Display menu and get user selection
        self.display_video_menu(videos)
        selected_indices = self.get_user_selection(videos)

        if not selected_indices:
            print("No videos selected. Exiting.")
            return

        # Process selected videos
        print(f"\nProcessing {len(selected_indices)} selected videos...")
        successful = 0
        failed = 0

        for i, video_idx in enumerate(selected_indices, 1):
            video_folder_path, video_name = videos[video_idx]

            print(f"\n{'-'*60}")
            print(f"Processing video {i}/{len(selected_indices)}: {video_name}")
            print(f"{'-'*60}")

            # Ask for confirmation before each video
            try:
                proceed = input(f"Label '{video_name}' now? [y]/n/q: ").strip().lower()
                if proceed == 'q':
                    print("Labeling session cancelled by user.")
                    break
                elif proceed == 'n':
                    print(f"Skipping {video_name}")
                    continue
            except KeyboardInterrupt:
                print("\nLabeling session interrupted.")
                break

            # Label the video using isolated subprocess
            success = self.label_single_video_isolated(video_folder_path, video_name)

            if success:
                successful += 1
            else:
                failed += 1
                # Ask if user wants to continue after failure
                try:
                    continue_choice = input("Continue with remaining videos? [y]/n: ").strip().lower()
                    if continue_choice == 'n':
                        break
                except KeyboardInterrupt:
                    print("\nLabeling session interrupted.")
                    break

        # Summary
        print(f"\n{'='*60}")
        print("LABELING SESSION SUMMARY")
        print(f"{'='*60}")
        print(f"Successfully labeled: {successful} videos")
        print(f"Failed: {failed} videos")
        print(f"Total processed: {successful + failed} videos")

        if successful > 0:
            print(f"\n{'='*60}")
            print("NEXT STEPS:")
            print(f"{'='*60}")
            print("1. Check your labeled data:")
            print(f"   dlc.check_labels(r'{self.config_path}')")
            print("2. Create training dataset:")
            print(f"   dlc.create_training_dataset(r'{self.config_path}')")
            print("3. Train the network:")
            print(f"   dlc.train_network(r'{self.config_path}')")


def main():
    """Main function to run the elegant labeling solution."""
    try:
        labeler = DeepLabCutLabeler(config_path)
        labeler.run_labeling_session()
    except KeyboardInterrupt:
        print("\nProgram interrupted by user.")
    except Exception as e:
        print(f"Error: {e}")
        print("Please check your configuration and try again.")

# Main execution
if __name__ == "__main__":
    main()
