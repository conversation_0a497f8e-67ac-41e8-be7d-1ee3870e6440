#!/usr/bin/env python3
"""
Master script to run all labeling scripts in sequence.
This ensures no deadlock issues by running each video separately.
"""

import subprocess
import sys
import os

scripts = [
    "label_mouse_video_ROH12F_D10_S50_S1.py",
    "label_mouse_video_ROH12F_D11_S50_S5.py",
    "label_mouse_video_ROH12F_D12_S50_S5.py",
    "label_mouse_video_ROH12F_D13_S50_P2.py",
    "label_mouse_video_ROH12F_D3_A50_S5.py",
    "label_mouse_video_ROH12F_D6_A50_S5.py",
    "label_mouse_video_ROH12F_D7_A50_S3.py",
    "label_mouse_video_ROH12F_D8_A50_S4.py",
    "label_mouse_video_ROH12F_D9_A50_S1.py",
    "label_mouse_video_ROX9M_D11_S50_S3.py",
    "label_mouse_video_ROX9M_D13_S50_S5.py",
    "label_mouse_video_ROX9M_D1_A100_S2.py",
    "label_mouse_video_ROX9M_D2_A50_S1.py",
    "label_mouse_video_ROX9M_D3_A50_S3.py",
    "label_mouse_video_ROX9M_D4_A50_S5.py",
    "label_mouse_video_ROX9M_D5_A50_S4.py",
    "label_mouse_video_ROX9M_D6_A50_S2.py",
    "label_mouse_video_ROX9M_D7_A50_S2.py",
    "label_mouse_video_ROX9M_D8_A50_S4.py",
    "label_mouse_video_ROX9M_D9_A50_S5.py",
    "label_VIDEO_20250103_AGC_2M_HFT_1_ACC75.py",
    "label_VIDEO_20250103_AGC_2M_HFT_2_ACC75.py",
    "label_VIDEO_20250103_AGC_3M_HFT_1_ACC100.py",
    "label_VIDEO_20250103_AGC_3M_HFT_2_ACC100.py",
    "label_VIDEO_20250103_AGC_3M_HFT_3_ACC100.py",
    "label_VIDEO_20250103_AGC_4M_HFT_1_ACC100.py",
    "label_VIDEO_20250104_AGC_4M_HFT_1_ACC80.py",
    "label_VIDEO_20250106_AGC_2M_HFT_3_80SAT100.py",
    "label_VIDEO_20250106_AGC_3M_HFT_2_80SAT100.py",
    "label_VIDEO_20250107_AGC_2M_HFT_1_80SAT100.py",
    "label_VIDEO_20250107_AGC_2M_HFT_2_80SAT100.py",
    "label_VIDEO_20250107_AGC_3M_HFT_1_80SAT100.py",
    "label_VIDEO_20250107_AGC_3M_HFT_2_80SAT100.py",
    "label_VIDEO_20250107_AGC_4M_HFT_1_80SAT100.py",
    "label_VIDEO_20250107_AGC_4M_HFT_2_80SAT100.py",
    "label_VIDEO_20250108_AGC_2M_HFT_1_80SAT100.py",
    "label_VIDEO_20250108_AGC_2M_HFT_2_80SAT100.py",
    "label_VIDEO_20250108_AGC_3M_HFT_1_80SAT100.py",
    "label_VIDEO_20250108_AGC_3M_HFT_2_80SAT100.py",
    "label_VIDEO_20250108_AGC_3M_HFT_3_80SAT100.py",
    "label_VIDEO_20250108_AGC_4M_HFT_1_80SAT100.py",
    "label_VIDEO_20250108_AGC_4M_HFT_2_80SAT100.py",
    "label_VIDEO_20250109_AGC_2M_HFT_1_80SAT100.py",
    "label_VIDEO_20250109_AGC_2M_HFT_2_80SAT100.py",
    "label_VIDEO_20250109_AGC_3M_HFT_1_80SAT100.py",
    "label_VIDEO_20250109_AGC_3M_HFT_2_80SAT100.py",
    "label_VIDEO_20250109_AGC_4M_HFT_1_80SAT100.py",
    "label_VIDEO_20250109_AGC_4M_HFT_2_80SAT100.py",
    "label_VIDEO_20250110_AGC_2M_HFT_1_80SAT100.py",
    "label_VIDEO_20250110_AGC_2M_HFT_2_80SAT100.py",
    "label_VIDEO_20250110_AGC_3M_HFT_1_80SAT100.py",
    "label_VIDEO_20250110_AGC_3M_HFT_2_80SAT100.py",
    "label_VIDEO_20250110_AGC_4M_HFT_1_80SAT100.py",
    "label_VIDEO_20250110_AGC_4M_HFT_2_80SAT100.py",
    "label_VIDEO_20250112_AGC_2M_HFT_1_80SAT100.py",
    "label_VIDEO_20250112_AGC_2M_HFT_2_80RAT75.py",
    "label_VIDEO_20250112_AGC_2M_HFT_3_80RAT75.py",
    "label_VIDEO_20250112_AGC_3M_HFT_1_80SAT100.py",
    "label_VIDEO_20250112_AGC_3M_HFT_2_80RAT75.py",
    "label_VIDEO_20250112_AGC_3M_HFT_3_80RAT75.py",
    "label_VIDEO_20250112_AGC_4M_HFT_1_80SAT100.py",
    "label_VIDEO_20250112_AGC_4M_HFT_2_80RAT75.py",
    "label_VIDEO_20250112_AGC_4M_HFT_3_80RAT75.py",
    "label_VIDEO_20250114_AGC_2M_HFT_1_80SAT100.py",
    "label_VIDEO_20250114_AGC_2M_HFT_2_80RAT75.py",
    "label_VIDEO_20250114_AGC_2M_HFT_3_80RAT75.py",
    "label_VIDEO_20250114_AGC_3M_HFT_1_80SAT100.py",
    "label_VIDEO_20250114_AGC_3M_HFT_2_80RAT75.py",
    "label_VIDEO_20250114_AGC_4M_HFT_1_80SAT100.py",
    "label_VIDEO_20250114_AGC_4M_HFT_2_80RAT75.py",
    "label_VIDEO_20250114_AGC_4M_HFT_3_80RAT75.py",
]

print("DeepLabCut Sequential Labeling")
print("=" * 50)
print(f"Will process {len(scripts)} videos in sequence")
print("Each video will open in a separate labeling session")
print("=" * 50)

for i, script in enumerate(scripts, 1):
    print(f"\n{'-'*50}")
    print(f"Starting labeling session {i}/{len(scripts)}: {script}")
    print(f"{'-'*50}")
    
    try:
        result = subprocess.run([sys.executable, script], check=True)
        print(f"SUCCESS: Completed: {script}")
    except subprocess.CalledProcessError as e:
        print(f"ERROR: Error with {script}: {e}")
        choice = input("Continue with next video? [y]/n: ").strip().lower()
        if choice == 'n':
            break

print("\n" + "=" * 50)
print("All labeling sessions completed!")
print("Next steps:")
print("1. Check labels: dlc.check_labels(config_path)")
print("2. Create training dataset: dlc.create_training_dataset(config_path)")
